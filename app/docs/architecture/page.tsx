import Link from 'next/link'

export default function ArchitecturePage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <header className="border-b border-gray-200 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">天罗文档</h1>
            <Link 
              href="/"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              返回主页
            </Link>
          </div>
        </header>

        <div className="flex">
          {/* Sidebar */}
          <nav className="w-64 py-8 pr-8">
            <div className="space-y-2">
              <Link 
                href="/docs"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                首页
              </Link>
              <Link 
                href="/docs/architecture"
                className="block px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md"
              >
                项目架构
              </Link>
              <Link 
                href="/docs/api"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                API文档
              </Link>
              <Link 
                href="/docs/deployment"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                部署指南
              </Link>
            </div>
          </nav>

          {/* Main Content */}
          <main className="flex-1 py-8 pl-8 border-l border-gray-200">
            <div className="prose prose-lg max-w-none">
              <h1>项目架构</h1>
              
              <p>本文档介绍天罗项目的整体架构设计和技术选型。</p>

              <h2>整体架构</h2>
              
              <p>天罗采用现代化的前后端分离架构，主要包含以下几个部分：</p>

              <h3>前端架构</h3>
              <ul>
                <li><strong>框架</strong>: Next.js 14 (App Router)</li>
                <li><strong>语言</strong>: TypeScript</li>
                <li><strong>样式</strong>: Tailwind CSS</li>
                <li><strong>UI组件</strong>: Radix UI + shadcn/ui</li>
                <li><strong>状态管理</strong>: React Context</li>
                <li><strong>表单处理</strong>: React Hook Form + Zod</li>
              </ul>

              <h3>后端服务</h3>
              <ul>
                <li><strong>数据库</strong>: Supabase PostgreSQL</li>
                <li><strong>认证</strong>: Supabase Auth</li>
                <li><strong>API</strong>: Supabase REST API</li>
                <li><strong>文件存储</strong>: Supabase Storage</li>
              </ul>

              <h2>目录结构</h2>
              
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`tianluo/
├── app/                    # Next.js App Router页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 可复用组件
│   ├── ui/               # UI基础组件
│   ├── auth-button.tsx   # 认证按钮
│   ├── header.tsx        # 页面头部
│   └── ...
├── contexts/             # React Context
│   └── auth-context.tsx  # 认证上下文
├── lib/                  # 工具库
│   ├── supabase.ts      # Supabase客户端
│   ├── utils.ts         # 工具函数
│   └── constants.ts     # 常量定义
├── public/              # 静态资源
└── types/               # TypeScript类型定义`}
              </pre>

              <h2>数据流</h2>
              
              <ol>
                <li><strong>用户交互</strong> → 前端组件</li>
                <li><strong>组件状态</strong> → React Context</li>
                <li><strong>API调用</strong> → Supabase客户端</li>
                <li><strong>数据处理</strong> → PostgreSQL数据库</li>
                <li><strong>响应返回</strong> → 前端更新UI</li>
              </ol>

              <h2>部署架构</h2>
              
              <ul>
                <li><strong>容器化</strong>: Docker + Docker Compose</li>
                <li><strong>构建</strong>: Next.js standalone输出</li>
                <li><strong>反向代理</strong>: 可配置Nginx</li>
                <li><strong>数据库</strong>: Supabase云服务</li>
              </ul>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
