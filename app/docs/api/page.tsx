import Link from 'next/link'

export default function ApiPage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <header className="border-b border-gray-200 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">天罗文档</h1>
            <Link 
              href="/"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              返回主页
            </Link>
          </div>
        </header>

        <div className="flex">
          {/* Sidebar */}
          <nav className="w-64 py-8 pr-8">
            <div className="space-y-2">
              <Link 
                href="/docs"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                首页
              </Link>
              <Link 
                href="/docs/architecture"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                项目架构
              </Link>
              <Link 
                href="/docs/api"
                className="block px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md"
              >
                API文档
              </Link>
              <Link 
                href="/docs/deployment"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                部署指南
              </Link>
            </div>
          </nav>

          {/* Main Content */}
          <main className="flex-1 py-8 pl-8 border-l border-gray-200">
            <div className="prose prose-lg max-w-none">
              <h1>API 文档</h1>
              
              <p>本文档介绍天罗项目的API接口和使用方法。</p>

              <h2>认证API</h2>

              <h3>用户注册</h3>
              
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`// POST /auth/signup
interface SignUpRequest {
  email: string;
  password: string;
  name?: string;
}

interface SignUpResponse {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}`}
              </pre>

              <p><strong>示例请求</strong>:</p>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123',
  options: {
    data: {
      name: '用户名'
    }
  }
})`}
              </pre>

              <h3>用户登录</h3>
              
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`// POST /auth/signin
interface SignInRequest {
  email: string;
  password: string;
}

interface SignInResponse {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}`}
              </pre>

              <p><strong>示例请求</strong>:</p>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password123'
})`}
              </pre>

              <h2>数据API</h2>

              <h3>获取用户信息</h3>
              
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`// GET /api/user/profile
interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}`}
              </pre>

              <p><strong>示例请求</strong>:</p>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .eq('id', userId)
  .single()`}
              </pre>

              <h2>错误处理</h2>
              
              <p>所有API都遵循统一的错误响应格式：</p>
              
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`interface ApiError {
  error: {
    message: string;
    code?: string;
    details?: any;
  }
}`}
              </pre>

              <h3>常见错误码</h3>
              
              <div className="overflow-x-auto">
                <table className="min-w-full border border-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="border border-gray-300 px-4 py-2 text-left">错误码</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">描述</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">解决方案</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>invalid_credentials</code></td>
                      <td className="border border-gray-300 px-4 py-2">登录凭据无效</td>
                      <td className="border border-gray-300 px-4 py-2">检查邮箱和密码</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>email_not_confirmed</code></td>
                      <td className="border border-gray-300 px-4 py-2">邮箱未验证</td>
                      <td className="border border-gray-300 px-4 py-2">检查邮箱验证链接</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2"><code>user_not_found</code></td>
                      <td className="border border-gray-300 px-4 py-2">用户不存在</td>
                      <td className="border border-gray-300 px-4 py-2">确认用户ID正确</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <h2>SDK使用</h2>
              
              <p>推荐使用Supabase JavaScript客户端：</p>
              
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)`}
              </pre>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
