import Link from 'next/link'

export default function DocsPage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <header className="border-b border-gray-200 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">天罗文档</h1>
            <Link 
              href="/"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              返回主页
            </Link>
          </div>
        </header>

        <div className="flex">
          {/* Sidebar */}
          <nav className="w-64 py-8 pr-8">
            <div className="space-y-2">
              <Link 
                href="/docs"
                className="block px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md"
              >
                首页
              </Link>
              <Link 
                href="/docs/architecture"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                项目架构
              </Link>
              <Link 
                href="/docs/api"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                API文档
              </Link>
              <Link 
                href="/docs/deployment"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                部署指南
              </Link>
            </div>
          </nav>

          {/* Main Content */}
          <main className="flex-1 py-8 pl-8 border-l border-gray-200">
            <div className="prose prose-lg max-w-none">
              <h1>欢迎来到天罗文档</h1>
              
              <p>天罗是一个现代化的Web应用程序，提供强大的功能和优秀的用户体验。</p>

              <h2>快速开始</h2>
              
              <p>这里是一些快速开始的指南：</p>

              <h3>安装</h3>
              <pre className="bg-gray-100 p-4 rounded-md">
                <code>npm install</code>
              </pre>

              <h3>开发</h3>
              <pre className="bg-gray-100 p-4 rounded-md">
                <code>npm run dev</code>
              </pre>

              <h3>构建</h3>
              <pre className="bg-gray-100 p-4 rounded-md">
                <code>npm run build</code>
              </pre>

              <h2>主要功能</h2>
              
              <ul>
                <li><strong>🚀 高性能</strong> - 基于Next.js构建，提供出色的性能</li>
                <li><strong>🎨 现代UI</strong> - 使用Tailwind CSS和Radix UI组件</li>
                <li><strong>🔐 安全认证</strong> - 集成Supabase认证系统</li>
                <li><strong>📱 响应式设计</strong> - 完美适配各种设备</li>
              </ul>

              <h2>技术栈</h2>
              
              <ul>
                <li><strong>前端框架</strong>: Next.js 14</li>
                <li><strong>样式</strong>: Tailwind CSS</li>
                <li><strong>UI组件</strong>: Radix UI</li>
                <li><strong>认证</strong>: Supabase Auth</li>
                <li><strong>数据库</strong>: Supabase</li>
                <li><strong>部署</strong>: Docker</li>
              </ul>

              <h2>开始探索</h2>
              
              <p>选择左侧菜单中的任意章节开始探索文档内容。</p>

              <hr />

              <p>如果你有任何问题或建议，欢迎在GitHub上提交Issue。</p>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
