import Link from 'next/link'

export default function DeploymentPage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <header className="border-b border-gray-200 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">天罗文档</h1>
            <Link 
              href="/"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              返回主页
            </Link>
          </div>
        </header>

        <div className="flex">
          {/* Sidebar */}
          <nav className="w-64 py-8 pr-8">
            <div className="space-y-2">
              <Link 
                href="/docs"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                首页
              </Link>
              <Link 
                href="/docs/architecture"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                项目架构
              </Link>
              <Link 
                href="/docs/api"
                className="block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                API文档
              </Link>
              <Link 
                href="/docs/deployment"
                className="block px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md"
              >
                部署指南
              </Link>
            </div>
          </nav>

          {/* Main Content */}
          <main className="flex-1 py-8 pl-8 border-l border-gray-200">
            <div className="prose prose-lg max-w-none">
              <h1>部署指南</h1>
              
              <p>本文档介绍如何部署天罗项目到不同的环境。</p>

              <h2>环境要求</h2>

              <h3>开发环境</h3>
              <ul>
                <li>Node.js 18+</li>
                <li>npm 或 yarn</li>
                <li>Git</li>
              </ul>

              <h3>生产环境</h3>
              <ul>
                <li>Docker</li>
                <li>Docker Compose</li>
                <li>2GB+ RAM</li>
                <li>10GB+ 存储空间</li>
              </ul>

              <h2>环境变量配置</h2>
              
              <p>创建 <code>.env.local</code> 文件并配置以下环境变量：</p>
              
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000`}
              </pre>

              <h2>本地开发部署</h2>

              <h3>1. 克隆项目</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`git clone https://github.com/riftcover/tianluo.git
cd tianluo`}
              </pre>

              <h3>2. 安装依赖</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                <code>npm install</code>
              </pre>

              <h3>3. 配置环境变量</h3>
              <p>复制 <code>.env.example</code> 到 <code>.env.local</code> 并填入正确的值。</p>

              <h3>4. 启动开发服务器</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                <code>npm run dev</code>
              </pre>
              
              <p>应用将在 <code>http://localhost:3000</code> 启动。</p>

              <h2>Docker部署</h2>

              <h3>1. 构建镜像</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                <code>docker build -t tianluo .</code>
              </pre>

              <h3>2. 运行容器</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`docker run -p 3000:3000 \\
  -e NEXT_PUBLIC_SUPABASE_URL=your_url \\
  -e NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key \\
  tianluo`}
              </pre>

              <h3>3. 使用Docker Compose</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                <code>docker-compose up -d</code>
              </pre>

              <h2>生产环境部署</h2>

              <h3>Vercel部署</h3>
              <ol>
                <li>连接GitHub仓库到Vercel</li>
                <li>配置环境变量</li>
                <li>自动部署</li>
              </ol>

              <h3>自托管部署</h3>
              
              <p><strong>1. 准备服务器</strong></p>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose`}
              </pre>

              <p><strong>2. 部署应用</strong></p>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`git clone https://github.com/riftcover/tianluo.git
cd tianluo
cp .env.example .env.local
# 编辑环境变量
docker-compose up -d`}
              </pre>

              <h2>故障排除</h2>

              <h3>常见问题</h3>
              
              <ol>
                <li><strong>端口冲突</strong> - 修改 <code>docker-compose.yml</code> 中的端口映射</li>
                <li><strong>环境变量未生效</strong> - 检查 <code>.env.local</code> 文件格式，重启容器</li>
                <li><strong>Supabase连接失败</strong> - 验证URL和密钥是否正确，检查网络连接</li>
              </ol>

              <h3>性能优化</h3>
              
              <ul>
                <li>启用CDN</li>
                <li>配置缓存策略</li>
                <li>优化图片资源</li>
                <li>使用生产环境构建</li>
              </ul>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
