# 部署指南

本文档介绍如何部署天罗项目到不同的环境。

## 环境要求

### 开发环境
- Node.js 18+
- npm 或 yarn
- Git

### 生产环境
- Docker
- Docker Compose
- 2GB+ RAM
- 10GB+ 存储空间

## 环境变量配置

创建 `.env.local` 文件并配置以下环境变量：

```bash
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 本地开发部署

### 1. 克隆项目

```bash
git clone https://github.com/riftcover/tianluo.git
cd tianluo
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

复制 `.env.example` 到 `.env.local` 并填入正确的值。

### 4. 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动。

## Docker部署

### 1. 构建镜像

```bash
docker build -t tianluo .
```

### 2. 运行容器

```bash
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_SUPABASE_URL=your_url \
  -e NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key \
  tianluo
```

### 3. 使用Docker Compose

```bash
docker-compose up -d
```

## 生产环境部署

### Vercel部署

1. 连接GitHub仓库到Vercel
2. 配置环境变量
3. 自动部署

### 自托管部署

1. **准备服务器**
   ```bash
   # 安装Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   
   # 安装Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **部署应用**
   ```bash
   git clone https://github.com/riftcover/tianluo.git
   cd tianluo
   cp .env.example .env.local
   # 编辑环境变量
   docker-compose up -d
   ```

3. **配置反向代理** (可选)
   
   使用Nginx配置反向代理：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 数据库迁移

如果需要运行数据库迁移：

```bash
# 使用Supabase CLI
npx supabase db push
```

## 监控和日志

### 查看应用日志

```bash
docker-compose logs -f app
```

### 健康检查

访问 `http://your-domain/api/health` 检查应用状态。

## 故障排除

### 常见问题

1. **端口冲突**
   - 修改 `docker-compose.yml` 中的端口映射

2. **环境变量未生效**
   - 检查 `.env.local` 文件格式
   - 重启容器

3. **Supabase连接失败**
   - 验证URL和密钥是否正确
   - 检查网络连接

### 性能优化

- 启用CDN
- 配置缓存策略
- 优化图片资源
- 使用生产环境构建
