# 项目架构

本文档介绍天罗项目的整体架构设计和技术选型。

## 整体架构

天罗采用现代化的前后端分离架构，主要包含以下几个部分：

### 前端架构

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **UI组件**: Radix UI + shadcn/ui
- **状态管理**: React Context
- **表单处理**: React Hook Form + Zod

### 后端服务

- **数据库**: Supabase PostgreSQL
- **认证**: Supabase Auth
- **API**: Supabase REST API
- **文件存储**: Supabase Storage

## 目录结构

```
tianluo/
├── app/                    # Next.js App Router页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 可复用组件
│   ├── ui/               # UI基础组件
│   ├── auth-button.tsx   # 认证按钮
│   ├── header.tsx        # 页面头部
│   └── ...
├── contexts/             # React Context
│   └── auth-context.tsx  # 认证上下文
├── lib/                  # 工具库
│   ├── supabase.ts      # Supabase客户端
│   ├── utils.ts         # 工具函数
│   └── constants.ts     # 常量定义
├── pages/               # Nextra文档页面
├── public/              # 静态资源
└── types/               # TypeScript类型定义
```

## 数据流

1. **用户交互** → 前端组件
2. **组件状态** → React Context
3. **API调用** → Supabase客户端
4. **数据处理** → PostgreSQL数据库
5. **响应返回** → 前端更新UI

## 部署架构

- **容器化**: Docker + Docker Compose
- **构建**: Next.js standalone输出
- **反向代理**: 可配置Nginx
- **数据库**: Supabase云服务

## 安全考虑

- **认证**: JWT Token验证
- **授权**: Row Level Security (RLS)
- **HTTPS**: 生产环境强制HTTPS
- **环境变量**: 敏感信息环境变量管理

## 性能优化

- **代码分割**: Next.js自动代码分割
- **图片优化**: Next.js Image组件
- **缓存策略**: 浏览器缓存 + CDN
- **懒加载**: 组件和路由懒加载
