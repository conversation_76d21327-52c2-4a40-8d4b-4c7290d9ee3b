# 欢迎来到天罗文档

天罗是一个现代化的Web应用程序，提供强大的功能和优秀的用户体验。

## 快速开始

这里是一些快速开始的指南：

### 安装

```bash
npm install
```

### 开发

```bash
npm run dev
```

### 构建

```bash
npm run build
```

## 主要功能

- 🚀 **高性能** - 基于Next.js构建，提供出色的性能
- 🎨 **现代UI** - 使用Tailwind CSS和Radix UI组件
- 🔐 **安全认证** - 集成Supabase认证系统
- 📱 **响应式设计** - 完美适配各种设备

## 技术栈

- **前端框架**: Next.js 14
- **样式**: Tailwind CSS
- **UI组件**: Radix UI
- **认证**: Supabase Auth
- **数据库**: Supabase
- **部署**: Docker

## 开始探索

选择左侧菜单中的任意章节开始探索文档内容。

---

如果你有任何问题或建议，欢迎在GitHub上提交Issue。
