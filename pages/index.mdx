# 欢迎来到天罗文档

天罗是一个现代化的Web应用程序，提供强大的功能和优秀的用户体验。

## 🚀 快速开始

这里是一些快速开始的指南：

### 安装

```bash
npm install
```

### 开发

```bash
npm run dev
```

### 构建

```bash
npm run build
```

## 🎯 主要功能

- **🚀 高性能** - 基于Next.js构建，提供出色的性能
- **🎨 现代UI** - 使用Tailwind CSS和Radix UI组件
- **🔐 安全认证** - 集成Supabase认证系统
- **📱 响应式设计** - 完美适配各种设备

## 🛠️ 技术栈

- **前端框架**: Next.js 14
- **样式**: Tailwind CSS
- **UI组件**: Radix UI
- **认证**: Supabase Auth
- **数据库**: Supabase
- **部署**: Docker

## 📚 文档导航

选择左侧菜单中的任意章节开始探索文档内容：

- [项目架构](/architecture) - 了解系统设计和技术选型
- [API文档](/api) - 详细的接口说明和使用示例
- [部署指南](/deployment) - 各种环境的部署方法

## 🤝 贡献

如果你有任何问题或建议，欢迎：

- 在GitHub上提交[Issue](https://github.com/riftcover/tianluo/issues)
- 创建Pull Request贡献代码
- 参与[Discussions](https://github.com/riftcover/tianluo/discussions)讨论

---

**开始探索** → 选择左侧菜单开始你的天罗之旅！
