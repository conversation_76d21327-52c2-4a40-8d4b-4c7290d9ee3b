# API 文档

本文档详细介绍天罗项目的API接口，包括认证、用户管理、数据操作等功能。

## 🔐 认证API

### 用户注册

注册新用户账户。

**接口信息**
- **方法**: `POST`
- **路径**: `/auth/signup`
- **认证**: 无需认证

**请求参数**

```typescript
interface SignUpRequest {
  email: string;        // 邮箱地址
  password: string;     // 密码（最少8位）
  name?: string;        // 用户名（可选）
}
```

**响应数据**

```typescript
interface SignUpResponse {
  user: User | null;           // 用户信息
  session: Session | null;     // 会话信息
  error: AuthError | null;     // 错误信息
}
```

**示例代码**

```javascript
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123',
  options: {
    data: {
      name: '张三'
    }
  }
})

if (error) {
  console.error('注册失败:', error.message)
} else {
  console.log('注册成功:', data.user)
}
```

### 用户登录

使用邮箱和密码登录。

**接口信息**
- **方法**: `POST`
- **路径**: `/auth/signin`
- **认证**: 无需认证

**请求参数**

```typescript
interface SignInRequest {
  email: string;        // 邮箱地址
  password: string;     // 密码
}
```

**示例代码**

```javascript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password123'
})

if (error) {
  console.error('登录失败:', error.message)
} else {
  console.log('登录成功:', data.user)
}
```

### 用户登出

退出当前用户会话。

**接口信息**
- **方法**: `POST`
- **路径**: `/auth/signout`
- **认证**: 需要认证

**示例代码**

```javascript
const { error } = await supabase.auth.signOut()

if (error) {
  console.error('登出失败:', error.message)
} else {
  console.log('登出成功')
}
```

### 密码重置

发送密码重置邮件。

**接口信息**
- **方法**: `POST`
- **路径**: `/auth/reset-password`
- **认证**: 无需认证

**示例代码**

```javascript
const { error } = await supabase.auth.resetPasswordForEmail(
  '<EMAIL>',
  {
    redirectTo: 'https://yourapp.com/reset-password'
  }
)
```

## 👤 用户管理API

### 获取用户信息

获取当前登录用户的详细信息。

**接口信息**
- **方法**: `GET`
- **路径**: `/api/user/profile`
- **认证**: 需要认证

**响应数据**

```typescript
interface UserProfile {
  id: string;              // 用户ID
  email: string;           // 邮箱
  name: string;            // 用户名
  avatar_url?: string;     // 头像URL
  created_at: string;      // 创建时间
  updated_at: string;      // 更新时间
}
```

**示例代码**

```javascript
const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .eq('id', user.id)
  .single()

if (error) {
  console.error('获取用户信息失败:', error.message)
} else {
  console.log('用户信息:', data)
}
```

### 更新用户信息

更新当前用户的个人信息。

**接口信息**
- **方法**: `PUT`
- **路径**: `/api/user/profile`
- **认证**: 需要认证

**请求参数**

```typescript
interface UpdateProfileRequest {
  name?: string;           // 用户名
  avatar_url?: string;     // 头像URL
}
```

**示例代码**

```javascript
const { data, error } = await supabase
  .from('profiles')
  .update({ 
    name: '新用户名',
    avatar_url: 'https://example.com/avatar.jpg'
  })
  .eq('id', user.id)

if (error) {
  console.error('更新失败:', error.message)
} else {
  console.log('更新成功:', data)
}
```

## 📊 数据查询API

### 分页查询

支持分页的数据查询。

**查询参数**

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `page` | number | 1 | 页码 |
| `limit` | number | 10 | 每页数量 |
| `sort` | string | 'created_at' | 排序字段 |
| `order` | string | 'desc' | 排序方向 |

**示例代码**

```javascript
const { data, error, count } = await supabase
  .from('table_name')
  .select('*', { count: 'exact' })
  .range(0, 9)  // 获取前10条
  .order('created_at', { ascending: false })

if (error) {
  console.error('查询失败:', error.message)
} else {
  console.log('数据:', data)
  console.log('总数:', count)
}
```

### 条件查询

支持多种条件的数据查询。

**示例代码**

```javascript
// 等值查询
const { data } = await supabase
  .from('table_name')
  .select('*')
  .eq('status', 'active')

// 模糊查询
const { data } = await supabase
  .from('table_name')
  .select('*')
  .ilike('name', '%关键词%')

// 范围查询
const { data } = await supabase
  .from('table_name')
  .select('*')
  .gte('created_at', '2024-01-01')
  .lte('created_at', '2024-12-31')

// 组合查询
const { data } = await supabase
  .from('table_name')
  .select('*')
  .eq('status', 'active')
  .in('category', ['tech', 'business'])
```

## ❌ 错误处理

### 错误响应格式

所有API错误都遵循统一的响应格式：

```typescript
interface ApiError {
  error: {
    message: string;      // 错误描述
    code?: string;        // 错误代码
    details?: any;        // 详细信息
    hint?: string;        // 解决提示
  }
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| `invalid_credentials` | 400 | 登录凭据无效 | 检查邮箱和密码是否正确 |
| `email_not_confirmed` | 400 | 邮箱未验证 | 检查邮箱中的验证链接 |
| `user_not_found` | 404 | 用户不存在 | 确认用户ID或邮箱正确 |
| `insufficient_permissions` | 403 | 权限不足 | 检查用户权限或登录状态 |
| `rate_limit_exceeded` | 429 | 请求频率超限 | 稍后重试或联系管理员 |
| `internal_server_error` | 500 | 服务器内部错误 | 联系技术支持 |

### 错误处理示例

```javascript
try {
  const { data, error } = await supabase
    .from('table_name')
    .select('*')
  
  if (error) {
    // 处理Supabase错误
    switch (error.code) {
      case 'PGRST116':
        console.error('数据不存在')
        break
      case 'PGRST301':
        console.error('权限不足')
        break
      default:
        console.error('未知错误:', error.message)
    }
    return
  }
  
  // 处理成功响应
  console.log('查询成功:', data)
  
} catch (error) {
  // 处理网络错误等异常
  console.error('请求异常:', error)
}
```

## 🔧 SDK使用

### 初始化客户端

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)
```

### 认证状态监听

```javascript
import { useEffect, useState } from 'react'

function useAuth() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 获取当前用户
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user)
      setLoading(false)
    })

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  return { user, loading }
}
```

## 📝 请求限制

### 频率限制

| 接口类型 | 限制 | 时间窗口 |
|----------|------|----------|
| 认证API | 10次/分钟 | 每个IP |
| 数据API | 100次/分钟 | 每个用户 |
| 文件上传 | 5次/分钟 | 每个用户 |

### 数据限制

- **单次查询**: 最多1000条记录
- **文件上传**: 单文件最大50MB
- **请求体**: 最大10MB

---

*API文档持续更新中，如有疑问请提交Issue*
