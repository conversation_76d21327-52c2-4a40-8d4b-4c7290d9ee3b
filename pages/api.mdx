# API 文档

本文档介绍天罗项目的API接口和使用方法。

## 认证API

### 用户注册

```typescript
// POST /auth/signup
interface SignUpRequest {
  email: string;
  password: string;
  name?: string;
}

interface SignUpResponse {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}
```

**示例请求**:
```javascript
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123',
  options: {
    data: {
      name: '用户名'
    }
  }
})
```

### 用户登录

```typescript
// POST /auth/signin
interface SignInRequest {
  email: string;
  password: string;
}

interface SignInResponse {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}
```

**示例请求**:
```javascript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password123'
})
```

### 用户登出

```typescript
// POST /auth/signout
interface SignOutResponse {
  error: AuthError | null;
}
```

**示例请求**:
```javascript
const { error } = await supabase.auth.signOut()
```

## 数据API

### 获取用户信息

```typescript
// GET /api/user/profile
interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}
```

**示例请求**:
```javascript
const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .eq('id', userId)
  .single()
```

### 更新用户信息

```typescript
// PUT /api/user/profile
interface UpdateProfileRequest {
  name?: string;
  avatar_url?: string;
}
```

**示例请求**:
```javascript
const { data, error } = await supabase
  .from('profiles')
  .update({ name: '新用户名' })
  .eq('id', userId)
```

## 错误处理

所有API都遵循统一的错误响应格式：

```typescript
interface ApiError {
  error: {
    message: string;
    code?: string;
    details?: any;
  }
}
```

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| `invalid_credentials` | 登录凭据无效 | 检查邮箱和密码 |
| `email_not_confirmed` | 邮箱未验证 | 检查邮箱验证链接 |
| `user_not_found` | 用户不存在 | 确认用户ID正确 |
| `insufficient_permissions` | 权限不足 | 检查用户权限 |

## 请求限制

- **认证API**: 每分钟最多10次请求
- **数据API**: 每分钟最多100次请求
- **文件上传**: 单文件最大10MB

## SDK使用

推荐使用Supabase JavaScript客户端：

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)
```
