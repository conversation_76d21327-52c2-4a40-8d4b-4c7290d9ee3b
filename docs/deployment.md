# 部署指南

本文档详细介绍如何将天罗项目部署到不同的环境，包括开发环境、测试环境和生产环境。

## 🎯 部署概览

天罗支持多种部署方式：

- **开发环境**: 本地开发服务器
- **云平台**: Vercel、Netlify等
- **容器化**: Docker + Docker Compose
- **自托管**: VPS、云服务器

## 📋 环境要求

### 最低要求

| 组件 | 要求 | 推荐 |
|------|------|------|
| **Node.js** | 18.0+ | 20.0+ |
| **内存** | 1GB | 2GB+ |
| **存储** | 5GB | 10GB+ |
| **网络** | 1Mbps | 10Mbps+ |

### 开发环境

- Node.js 18+
- npm 或 yarn 或 pnpm
- Git
- 代码编辑器（推荐VS Code）

### 生产环境

- Docker 20+
- Docker Compose 2+
- 反向代理（Nginx/Caddy）
- SSL证书

## ⚙️ 环境变量配置

### 必需变量

创建 `.env.local` 文件：

```bash
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 应用配置
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXTAUTH_SECRET=your-nextauth-secret
```

### 可选变量

```bash
# 分析和监控
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
SENTRY_DSN=https://your-sentry-dsn

# 邮件服务
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 文件存储
NEXT_PUBLIC_STORAGE_BUCKET=your-bucket-name
```

## 🚀 本地开发部署

### 1. 克隆项目

```bash
git clone https://github.com/riftcover/tianluo.git
cd tianluo
```

### 2. 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install

# 或使用pnpm
pnpm install
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

### 4. 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动。

### 5. 验证部署

访问以下页面确认功能正常：

- 首页: `http://localhost:3000`
- 登录: `http://localhost:3000/login`
- 注册: `http://localhost:3000/register`
- 文档: `http://localhost:3000/docs`

## ☁️ 云平台部署

### Vercel部署

Vercel是推荐的部署平台，提供零配置部署。

**步骤：**

1. **连接GitHub**
   - 登录 [Vercel](https://vercel.com)
   - 导入GitHub仓库

2. **配置环境变量**
   ```bash
   # 在Vercel Dashboard中添加
   NEXT_PUBLIC_SUPABASE_URL=your-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-key
   ```

3. **部署**
   - 点击"Deploy"按钮
   - 等待构建完成

4. **自定义域名**（可选）
   - 在Settings中添加自定义域名
   - 配置DNS记录

### Netlify部署

1. **连接仓库**
   - 登录Netlify
   - 选择"New site from Git"

2. **构建设置**
   ```bash
   Build command: npm run build
   Publish directory: .next
   ```

3. **环境变量**
   - 在Site settings中添加环境变量

## 🐳 Docker部署

### 单容器部署

**1. 构建镜像**

```bash
docker build -t tianluo .
```

**2. 运行容器**

```bash
docker run -d \
  --name tianluo-app \
  -p 3000:3000 \
  -e NEXT_PUBLIC_SUPABASE_URL=your-url \
  -e NEXT_PUBLIC_SUPABASE_ANON_KEY=your-key \
  tianluo
```

### Docker Compose部署

**1. 创建docker-compose.yml**

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

**2. 启动服务**

```bash
docker-compose up -d
```

## 🖥️ 自托管部署

### VPS部署

**1. 准备服务器**

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Nginx
sudo apt install nginx -y
```

**2. 配置Nginx**

创建 `/etc/nginx/sites-available/tianluo`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

**3. 启用站点**

```bash
sudo ln -s /etc/nginx/sites-available/tianluo /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

**4. 配置SSL**

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和维护

### 健康检查

创建健康检查端点：

```javascript
// pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
}
```

### 日志管理

**查看应用日志**

```bash
# Docker容器日志
docker logs tianluo-app -f

# Docker Compose日志
docker-compose logs -f app

# 系统日志
sudo journalctl -u nginx -f
```

### 备份策略

**1. 数据库备份**

Supabase提供自动备份，也可以手动备份：

```bash
# 使用pg_dump备份
pg_dump -h your-db-host -U postgres -d your-db > backup.sql
```

**2. 代码备份**

```bash
# Git备份
git push origin main

# 文件备份
tar -czf backup-$(date +%Y%m%d).tar.gz /path/to/app
```

## 🔧 故障排除

### 常见问题

**1. 端口冲突**

```bash
# 查看端口占用
sudo netstat -tlnp | grep :3000

# 修改端口
docker run -p 3001:3000 tianluo
```

**2. 环境变量未生效**

```bash
# 检查环境变量
docker exec tianluo-app env | grep SUPABASE

# 重启容器
docker restart tianluo-app
```

**3. 构建失败**

```bash
# 清理Docker缓存
docker system prune -a

# 重新构建
docker build --no-cache -t tianluo .
```

### 性能优化

**1. 启用压缩**

在Nginx配置中添加：

```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

**2. 缓存配置**

```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

**3. 资源优化**

```bash
# 分析包大小
npm run build
npx @next/bundle-analyzer

# 优化图片
npm install next-optimized-images
```

---

*部署文档会根据最佳实践持续更新*
