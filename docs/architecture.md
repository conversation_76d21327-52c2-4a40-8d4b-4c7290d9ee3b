# 项目架构

本文档详细介绍天罗项目的整体架构设计、技术选型和设计理念。

## 🏗️ 整体架构

天罗采用现代化的前后端分离架构，基于云原生设计理念构建：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │    │   Supabase      │    │   部署环境       │
│                │    │                │    │                │
│  Next.js 14    │◄──►│  PostgreSQL    │    │   Docker       │
│  TypeScript    │    │  Auth Service  │    │   Vercel       │
│  Tailwind CSS  │    │  Storage       │    │   自托管        │
│  Radix UI      │    │  Edge Functions│    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 技术栈

### 前端技术栈

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Next.js** | 14.x | React框架 | 全栈能力、优秀性能、SEO友好 |
| **TypeScript** | 5.x | 类型系统 | 类型安全、开发体验、代码质量 |
| **Tailwind CSS** | 3.x | CSS框架 | 快速开发、一致性、可维护性 |
| **Radix UI** | Latest | 无头组件 | 可访问性、自定义性、质量 |
| **React Hook Form** | 7.x | 表单管理 | 性能优秀、API简洁 |
| **Zod** | 3.x | 数据验证 | TypeScript集成、运行时安全 |

### 后端服务

| 服务 | 提供商 | 用途 | 优势 |
|------|--------|------|------|
| **数据库** | Supabase PostgreSQL | 数据存储 | 关系型、ACID、扩展性 |
| **认证** | Supabase Auth | 用户管理 | 开箱即用、安全性、多种登录方式 |
| **存储** | Supabase Storage | 文件管理 | CDN加速、权限控制 |
| **API** | Supabase REST | 数据接口 | 自动生成、实时订阅 |

## 📁 目录结构

```
tianluo/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 认证相关页面组
│   │   ├── login/         # 登录页面
│   │   ├── register/      # 注册页面
│   │   └── layout.tsx     # 认证布局
│   ├── docs/              # 文档页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React组件
│   ├── ui/               # 基础UI组件
│   │   ├── button.tsx    # 按钮组件
│   │   ├── input.tsx     # 输入框组件
│   │   └── ...           # 其他UI组件
│   ├── auth-button.tsx   # 认证按钮
│   ├── header.tsx        # 页面头部
│   └── ...               # 业务组件
├── contexts/             # React Context
│   └── auth-context.tsx  # 认证状态管理
├── lib/                  # 工具库
│   ├── supabase.ts      # Supabase客户端
│   ├── utils.ts         # 通用工具函数
│   └── constants.ts     # 常量定义
├── types/               # TypeScript类型
│   ├── auth.ts          # 认证相关类型
│   └── database.ts      # 数据库类型
├── docs/                # Markdown文档
├── public/              # 静态资源
└── ...                  # 配置文件
```

## 🔄 数据流架构

### 1. 用户交互流程

```mermaid
graph TD
    A[用户操作] --> B[React组件]
    B --> C[Context状态]
    C --> D[Supabase客户端]
    D --> E[API请求]
    E --> F[PostgreSQL]
    F --> G[响应数据]
    G --> H[UI更新]
```

### 2. 认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 客户端
    participant S as Supabase Auth
    participant D as 数据库
    
    U->>C: 登录请求
    C->>S: 验证凭据
    S->>D: 查询用户
    D-->>S: 用户信息
    S-->>C: JWT Token
    C-->>U: 登录成功
```

## 🏛️ 架构设计原则

### 1. 关注点分离
- **展示层**: React组件专注UI渲染
- **业务层**: Context管理应用状态
- **数据层**: Supabase处理数据持久化

### 2. 组件化设计
- **原子组件**: 最小可复用单元
- **分子组件**: 原子组件组合
- **有机体组件**: 完整功能模块

### 3. 类型安全
- **编译时检查**: TypeScript类型系统
- **运行时验证**: Zod数据验证
- **API类型**: Supabase自动生成类型

### 4. 性能优化
- **代码分割**: Next.js自动分割
- **懒加载**: 动态导入组件
- **缓存策略**: 浏览器和CDN缓存

## 🔒 安全架构

### 1. 认证安全
- **JWT Token**: 无状态认证
- **刷新机制**: 自动token刷新
- **会话管理**: 安全的会话处理

### 2. 数据安全
- **RLS策略**: 行级安全策略
- **权限控制**: 基于角色的访问控制
- **数据验证**: 客户端和服务端双重验证

### 3. 传输安全
- **HTTPS**: 强制加密传输
- **CORS**: 跨域请求控制
- **CSP**: 内容安全策略

## 📈 扩展性设计

### 1. 水平扩展
- **无状态设计**: 支持多实例部署
- **CDN分发**: 静态资源全球分发
- **数据库分片**: 支持数据水平分割

### 2. 垂直扩展
- **模块化架构**: 功能模块独立
- **插件系统**: 支持功能扩展
- **配置驱动**: 环境配置分离

## 🔧 开发工具链

### 1. 开发环境
- **热重载**: 快速开发反馈
- **类型检查**: 实时类型验证
- **代码格式化**: Prettier自动格式化

### 2. 构建工具
- **Webpack**: 模块打包
- **SWC**: 快速编译
- **Tree Shaking**: 死代码消除

### 3. 质量保证
- **ESLint**: 代码质量检查
- **Husky**: Git钩子管理
- **CI/CD**: 自动化部署

## 📊 监控与观测

### 1. 性能监控
- **Core Web Vitals**: 用户体验指标
- **Bundle分析**: 包大小监控
- **加载性能**: 页面加载时间

### 2. 错误监控
- **错误边界**: React错误捕获
- **日志收集**: 结构化日志
- **异常报告**: 实时错误通知

---

*本文档会随着项目发展持续更新*
