# 天罗文档

欢迎来到天罗项目的文档中心。这里包含了项目的完整文档，帮助你快速了解和使用天罗。

## 📚 文档目录

- [项目架构](./architecture.md) - 了解天罗的整体架构设计
- [API文档](./api.md) - 详细的API接口说明
- [部署指南](./deployment.md) - 如何部署天罗到各种环境
- [开发指南](./development.md) - 本地开发环境搭建

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 🏗️ 项目概述

天罗是一个现代化的Web应用程序，采用以下技术栈：

- **前端**: Next.js 14 + TypeScript
- **样式**: Tailwind CSS
- **UI组件**: Radix UI + shadcn/ui
- **认证**: Supabase Auth
- **数据库**: Supabase PostgreSQL
- **部署**: Docker

## 🎯 主要功能

- 🔐 **用户认证** - 完整的用户注册、登录、密码重置功能
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🎨 **现代UI** - 基于设计系统的一致性界面
- ⚡ **高性能** - 优化的加载速度和用户体验
- 🔒 **安全性** - 企业级安全标准

## 📖 如何使用文档

1. **新手入门**: 从[项目架构](./architecture.md)开始了解整体设计
2. **开发者**: 查看[API文档](./api.md)了解接口使用
3. **运维人员**: 参考[部署指南](./deployment.md)进行部署
4. **贡献者**: 阅读[开发指南](./development.md)参与开发

## 🤝 贡献

如果你发现文档中的错误或希望改进内容，欢迎：

1. 提交Issue报告问题
2. 创建Pull Request贡献代码
3. 在GitHub Discussions中讨论

## 📞 联系我们

- GitHub: [https://github.com/riftcover/tianluo](https://github.com/riftcover/tianluo)
- Issues: [提交问题](https://github.com/riftcover/tianluo/issues)

---

*最后更新: 2024年*
