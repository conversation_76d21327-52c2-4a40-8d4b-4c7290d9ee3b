# 天罗文档系统

本项目已集成Nextra文档系统，提供了完整的文档管理功能。

## 文档结构

```
pages/
├── _meta.json          # 导航配置
├── index.mdx          # 文档首页
├── architecture.mdx   # 项目架构
├── api.mdx           # API文档
└── deployment.mdx    # 部署指南
```

## 如何使用

### 1. 安装依赖

首先需要安装Nextra相关依赖：

```bash
npm install nextra nextra-theme-docs
```

### 2. 启动文档服务

```bash
npm run docs:dev
```

文档将在 `http://localhost:3000` 启动。

### 3. 添加新文档

在 `pages/` 目录下创建新的 `.mdx` 文件，然后在 `pages/_meta.json` 中添加导航配置：

```json
{
  "index": "首页",
  "architecture": "项目架构",
  "api": "API文档",
  "deployment": "部署指南",
  "new-page": "新页面标题"
}
```

### 4. 文档语法

Nextra支持标准的Markdown语法，以及一些扩展功能：

#### 代码块

```javascript
const example = "代码示例";
```

#### 提示框

> **注意**: 这是一个重要提示

#### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 值1 | 值2 | 值3 |

#### 链接

[内部链接](/api)
[外部链接](https://github.com)

## 配置说明

### theme.config.tsx

主题配置文件，包含：
- 网站标题和Logo
- 导航链接
- 页脚信息
- SEO设置

### next.config.mjs

Next.js配置文件，已集成Nextra插件。

## 部署文档

### 开发环境

```bash
npm run docs:dev
```

### 生产环境

```bash
npm run docs:build
npm run docs:start
```

### Docker部署

文档系统与主应用共享同一个Docker容器，无需额外配置。

## 自定义样式

可以在 `pages/` 目录下创建 `styles/` 文件夹来添加自定义CSS样式。

## 多语言支持

Nextra支持多语言，可以通过配置实现中英文切换。

## 搜索功能

Nextra内置了全文搜索功能，用户可以通过快捷键 `Ctrl+K` 或 `Cmd+K` 打开搜索。

## 更多功能

- 自动生成目录
- 代码高亮
- 响应式设计
- 暗色模式
- 打印友好

## 参考资源

- [Nextra官方文档](https://nextra.site)
- [Nextra主题文档](https://nextra.site/docs/docs-theme)
- [MDX语法指南](https://mdxjs.com)
