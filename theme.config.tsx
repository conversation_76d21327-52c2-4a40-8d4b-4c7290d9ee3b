import React from 'react'

const config = {
  logo: <span>天罗文档</span>,
  project: {
    link: 'https://github.com/riftcover/tianluo',
  },
  docsRepositoryBase: 'https://github.com/riftcover/tianluo/tree/main',
  footer: {
    text: '天罗项目文档 © 2024',
  },
  useNextSeoProps() {
    return {
      titleTemplate: '%s – 天罗文档'
    }
  },
  head: (
    <>
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta property="og:title" content="天罗文档" />
      <meta property="og:description" content="天罗项目的完整文档" />
    </>
  ),
  editLink: {
    text: '在 GitHub 上编辑此页'
  },
  feedback: {
    content: '有问题？给我们反馈 →',
    labels: 'feedback'
  }
}

export default config
